from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.hooks.base import BaseHook
from datetime import datetime, timedelta, timezone
import logging
import re
import os
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON>Error, ClientError

# -----------------------
# Configuration
# -----------------------
BUCKET = "solvei8-stateful-backups"
PREFIXES = [
    "mongo/prod/full_backups_mongo_plani8-4/",
    "mongo/prod/full_backups_prod_mongo-3/",
    "mongo/prod/full_backups_prod_mongo-4/",
    "mongo/stage/full_backups_mongo-3-3/",
    "mongo/stage/full_backups_mongo-4/",
]

# -----------------------
# Logging Setup
# -----------------------
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

# -----------------------
# S3 Client
# -----------------------
def get_s3_client(aws_conn_id='s3_creds'):
    """
    Initializes and returns a boto3 S3 client using environment variables or Airflow connections.

    Args:
        aws_conn_id (str): Airflow connection ID for AWS credentials (fallback)

    Returns:
        boto3.client: Configured S3 client
    """
    try:
        # Check what environment variables are available
        aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')

        logger.info(f"Environment check - AWS_ACCESS_KEY_ID present: {bool(aws_access_key_id)}")
        logger.info(f"Environment check - AWS_SECRET_ACCESS_KEY present: {bool(aws_secret_access_key)}")

        if aws_access_key_id:
            logger.info(f"Found AWS_ACCESS_KEY_ID in environment: {aws_access_key_id[:10]}...")

        # Force use of Airflow connection instead of environment variables
        logger.info(f"Using AWS credentials from Airflow connection: {aws_conn_id}")
        s3_hook = S3Hook(aws_conn_id=aws_conn_id)
        s3_client = s3_hook.get_conn()

        # Verify credentials by calling AWS STS to get caller identity
        try:
            # Use STS to get the current user identity
            sts_response = s3_client.meta.client.get_caller_identity() if hasattr(s3_client, 'meta') else None
            if not sts_response:
                # Alternative approach using boto3 directly
                import boto3
                sts_client = boto3.client('sts',
                    aws_access_key_id=s3_hook.get_credentials().access_key if hasattr(s3_hook, 'get_credentials') else None,
                    aws_secret_access_key=s3_hook.get_credentials().secret_key if hasattr(s3_hook, 'get_credentials') else None,
                    region_name='ap-southeast-1'
                )
                sts_response = sts_client.get_caller_identity()

            if sts_response:
                logger.info(f"🔐 AWS Identity Verification:")
                logger.info(f"   User ARN: {sts_response.get('Arn', 'Unknown')}")
                logger.info(f"   Account: {sts_response.get('Account', 'Unknown')}")
                logger.info(f"   User ID: {sts_response.get('UserId', 'Unknown')}")

        except Exception as cred_error:
            logger.warning(f"Could not verify AWS identity: {cred_error}")
            # Try to get basic credential info from S3Hook
            try:
                if hasattr(s3_hook, 'get_credentials'):
                    creds = s3_hook.get_credentials()
                    if creds and hasattr(creds, 'access_key'):
                        access_key = creds.access_key
                        secret_key = creds.secret_key if hasattr(creds, 'secret_key') else 'Not available'
                        logger.info(f"✅ Using Access Key ID: {access_key[:10]}...{access_key[-4:] if len(access_key) > 14 else '***'}")
                        if secret_key and secret_key != 'Not available':
                            logger.info(f"✅ Using Secret Key: {secret_key[:10]}...{secret_key[-4:] if len(secret_key) > 14 else '***'}")
                        else:
                            logger.info(f"✅ Secret Key: {secret_key}")
            except Exception as fallback_error:
                logger.warning(f"Could not retrieve any credential info: {fallback_error}")

        # Additional method to get credentials from Airflow connection directly
        try:
            connection = BaseHook.get_connection(aws_conn_id)
            if connection:
                logger.info(f"🔗 Airflow Connection Details:")
                logger.info(f"   Connection ID: {connection.conn_id}")
                logger.info(f"   Connection Type: {connection.conn_type}")
                if connection.login:
                    logger.info(f"   Access Key (login): {connection.login[:10]}...{connection.login[-4:] if len(connection.login) > 14 else '***'}")
                if connection.password:
                    logger.info(f"   Secret Key (password): {connection.password[:10]}...{connection.password[-4:] if len(connection.password) > 14 else '***'}")
                if connection.extra:
                    logger.info(f"   Extra config: {connection.extra}")
        except Exception as conn_error:
            logger.warning(f"Could not retrieve Airflow connection details: {conn_error}")

        return s3_client

    except Exception as e:
        logger.error(f"Failed to initialize S3 client: {e}")
        logger.info("Make sure you have AWS credentials in environment variables or configured the AWS connection in Airflow Admin -> Connections")
        raise

# -----------------------
# Backup Folder Logic
# -----------------------
def get_backup_folders(s3_client, bucket_name, prefix):
    all_backups = []
    pattern = re.compile(r'mongo-full-backup-(\d{8})T\d{6}/')
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/')
        for page in pages:
            for common_prefix in page.get('CommonPrefixes', []):
                folder = common_prefix['Prefix']
                match = pattern.search(folder)
                if match:
                    date_str = match.group(1)
                    try:
                        folder_date = datetime.strptime(date_str, "%Y%m%d").date()
                        all_backups.append((folder_date, folder))
                    except ValueError:
                        logger.warning(f"Could not parse date from folder name: {folder}")
    except (BotoCoreError, ClientError) as e:
        logger.error(f"Error listing objects for prefix {prefix}: {e}")
    return all_backups

# -----------------------
# Retention Policy Logic
# -----------------------
def get_keep_dates(all_backups, num_latest, num_sundays):
    if not all_backups:
        return set()
    sorted_backups = sorted(all_backups, key=lambda x: x[0], reverse=True)
    latest_dates = set()
    for date, _ in sorted_backups:
        if len(latest_dates) < num_latest:
            latest_dates.add(date)
        else:
            break
    sundays_to_keep = set()
    current_date = datetime.now(timezone.utc).date()
    while len(sundays_to_keep) < num_sundays:
        if current_date.weekday() == 6:  # Sunday is 6
            sundays_to_keep.add(current_date)
        current_date -= timedelta(days=1)
    return latest_dates.union(sundays_to_keep)

def find_deletions(all_backups, keep_dates):
    to_delete = []
    for date, folder in all_backups:
        if date not in keep_dates:
            to_delete.append((date, folder))
    return to_delete

# -----------------------
# Main Airflow Callable
# -----------------------
def s3_retention_callable(**context):
    """
    This function is called by the Airflow PythonOperator.
    It receives parameters from op_kwargs via context.
    """
    # Extract parameters from context
    num_latest = context.get('num_latest', 6)
    num_sundays = context.get('num_sundays', 4)
    s3_client = get_s3_client()
    
    logger.info(f"Starting S3 backup retention for bucket: {BUCKET}")
    logger.info(f"Retention Policy: Keep latest {num_latest} and last {num_sundays} Sundays.")

    for prefix in PREFIXES:
        logger.info(f"\n--- Processing prefix: {prefix} ---")
        all_backups_for_prefix = get_backup_folders(s3_client, BUCKET, prefix)
        if not all_backups_for_prefix:
            logger.info(f"No backup folders found. Skipping.")
            continue

        keep_dates_for_prefix = get_keep_dates(all_backups_for_prefix, num_latest, num_sundays)
        to_delete_for_prefix = find_deletions(all_backups_for_prefix, keep_dates_for_prefix)

        logger.info(f"--- Deleting {len(to_delete_for_prefix)} folders in {prefix} ---")
        for date, folder in to_delete_for_prefix:
            logger.info(f"DELETING: s3://{BUCKET}/{folder} (Date: {date})")
            # To delete a "folder", you must delete all objects within it.
            try:
                paginator = s3_client.get_paginator('list_objects_v2')
                pages = paginator.paginate(Bucket=BUCKET, Prefix=folder)
                objects_to_delete = {'Objects': [{'Key': obj['Key']} for page in pages for obj in page.get('Contents', [])]}
                
                if objects_to_delete['Objects']:
                    # s3_client.delete_objects(Bucket=BUCKET, Delete=objects_to_delete)
                    logger.info(f"Successfully deleted objects in s3://{BUCKET}/{folder}")
            except (BotoCoreError, ClientError) as e:
                logger.error(f"Failed to delete objects in s3://{BUCKET}/{folder}: {e}")

    logger.info("S3 backup retention script finished.")

# -----------------------
# DAG Definition
# -----------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="s3_retention",
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['s3', 'retention', 'utility'],
    description="Deletes old S3 backups based on a retention policy.",
) as dag:
    run_retention_script = PythonOperator(
        task_id="run_s3_retention_script",
        python_callable=s3_retention_callable,
        op_kwargs={
            'num_latest': 6,
            'num_sundays': 4,
        }
    )


