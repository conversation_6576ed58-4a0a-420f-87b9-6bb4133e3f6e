from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.hooks.base import <PERSON>Hook
from datetime import datetime, timedelta, timezone
import logging
import re
import boto3
from botocore.exceptions import Bo<PERSON>CoreError, ClientError

# -----------------------
# Configuration
# -----------------------
BUCKET = "solvei8-stateful-backups"
PREFIXES = [
    "mongo/prod/full_backups_mongo_plani8-4/",
    "mongo/prod/full_backups_prod_mongo-3/",
    "mongo/prod/full_backups_prod_mongo-4/",
    "mongo/stage/full_backups_mongo-3-3/",
    "mongo/stage/full_backups_mongo-4/",
]

logger = logging.getLogger(__name__)

# -----------------------
# S3 Client
# -----------------------
def get_s3_client(aws_conn_id='s3_creds'):
    """Get S3 client from Airflow connection"""
    connection = BaseHook.get_connection(aws_conn_id)
    extra_config = connection.extra_dejson if hasattr(connection, 'extra_dejson') else {}

    session_token = extra_config.get('aws_session_token')
    region_name = extra_config.get('region_name', 'ap-southeast-1')

    return boto3.client(
        's3',
        aws_access_key_id=connection.login,
        aws_secret_access_key=connection.password,
        aws_session_token=session_token,
        region_name=region_name
    )

# -----------------------
# Backup Folder Logic
# -----------------------
def get_backup_folders(s3_client, bucket_name, prefix):
    all_backups = []
    pattern = re.compile(r'mongo-full-backup-(\d{8})T\d{6}/')
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/')
        for page in pages:
            for common_prefix in page.get('CommonPrefixes', []):
                folder = common_prefix['Prefix']
                match = pattern.search(folder)
                if match:
                    date_str = match.group(1)
                    try:
                        folder_date = datetime.strptime(date_str, "%Y%m%d").date()
                        all_backups.append((folder_date, folder))
                    except ValueError:
                        logger.warning(f"Could not parse date from folder name: {folder}")
    except (BotoCoreError, ClientError) as e:
        logger.error(f"Error listing objects for prefix {prefix}: {e}")
    return all_backups

# -----------------------
# Retention Policy Logic
# -----------------------
def get_keep_dates(all_backups, num_latest, num_sundays):
    if not all_backups:
        return set()
    sorted_backups = sorted(all_backups, key=lambda x: x[0], reverse=True)
    latest_dates = set()
    for date, _ in sorted_backups:
        if len(latest_dates) < num_latest:
            latest_dates.add(date)
        else:
            break
    sundays_to_keep = set()
    current_date = datetime.now(timezone.utc).date()
    while len(sundays_to_keep) < num_sundays:
        if current_date.weekday() == 6:  # Sunday is 6
            sundays_to_keep.add(current_date)
        current_date -= timedelta(days=1)
    return latest_dates.union(sundays_to_keep)

def find_deletions(all_backups, keep_dates):
    to_delete = []
    for date, folder in all_backups:
        if date not in keep_dates:
            to_delete.append((date, folder))
    return to_delete

# -----------------------
# Main Airflow Callable
# -----------------------
def s3_retention_callable(**context):
    """
    This function is called by the Airflow PythonOperator.
    It receives parameters from op_kwargs via context.
    """
    # Extract parameters from context
    num_latest = context.get('num_latest', 6)
    num_sundays = context.get('num_sundays', 4)
    s3_client = get_s3_client()
    
    logger.info(f"Starting S3 retention: Keep {num_latest} latest + {num_sundays} Sundays")

    for prefix in PREFIXES:
        logger.info(f"Processing: {prefix}")
        all_backups = get_backup_folders(s3_client, BUCKET, prefix)
        if not all_backups:
            logger.info("No backups found")
            continue

        keep_dates = get_keep_dates(all_backups, num_latest, num_sundays)
        to_delete = find_deletions(all_backups, keep_dates)

        logger.info(f"Deleting {len(to_delete)} folders")
        for _, folder in to_delete:
            try:
                paginator = s3_client.get_paginator('list_objects_v2')
                pages = paginator.paginate(Bucket=BUCKET, Prefix=folder)
                objects_to_delete = {'Objects': [{'Key': obj['Key']} for page in pages for obj in page.get('Contents', [])]}

                if objects_to_delete['Objects']:
                    s3_client.delete_objects(Bucket=BUCKET, Delete=objects_to_delete)
                    logger.info(f"Deleted: {folder}")
            except Exception as e:
                logger.error(f"Failed to delete {folder}: {e}")

    logger.info("S3 retention completed")

# -----------------------
# DAG Definition
# -----------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="s3_retention",
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['s3', 'retention', 'utility'],
    description="Deletes old S3 backups based on a retention policy.",
) as dag:
    run_retention_script = PythonOperator(
        task_id="run_s3_retention_script",
        python_callable=s3_retention_callable,
        op_kwargs={
            'num_latest': 6,
            'num_sundays': 4,
        }
    )


