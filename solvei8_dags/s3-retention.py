# S3 Backup Retention DAG - Optimized Version
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.hooks.base import BaseHook
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import re
import boto3

# Configuration
BUCKET = "solvei8-stateful-backups"
PREFIXES = [
    "mongo/prod/full_backups_mongo_plani8-4/",
    "mongo/prod/full_backups_prod_mongo-3/",
    "mongo/prod/full_backups_prod_mongo-4/",
    "mongo/stage/full_backups_mongo-3-3/",
    "mongo/stage/full_backups_mongo-4/",
]

logger = logging.getLogger(__name__)

# Core Functions
def get_s3_client(aws_conn_id='s3_creds'):
    """Get S3 client from Airflow connection"""
    connection = BaseHook.get_connection(aws_conn_id)
    extra_config = connection.extra_dejson if hasattr(connection, 'extra_dejson') else {}

    return boto3.client(
        's3',
        aws_access_key_id=connection.login,
        aws_secret_access_key=connection.password,
        aws_session_token=extra_config.get('aws_session_token'),
        region_name=extra_config.get('region_name', 'ap-southeast-1')
    )

def get_backup_folders(s3_client, bucket_name, prefix, max_folders=50):
    """Get backup folders with early termination"""
    all_backups = []
    pattern = re.compile(r'mongo-full-backup-(\d{8})T\d{6}/')

    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/')

        for page in pages:
            for common_prefix in page.get('CommonPrefixes', []):
                folder = common_prefix['Prefix']
                match = pattern.search(folder)
                if match:
                    date_str = match.group(1)
                    try:
                        folder_date = datetime.strptime(date_str, "%Y%m%d").date()
                        all_backups.append((folder_date, folder))

                        if len(all_backups) >= max_folders:
                            break
                    except ValueError:
                        logger.warning(f"Could not parse date: {folder}")

            if len(all_backups) >= max_folders:
                break

    except Exception as e:
        logger.error(f"Error listing {prefix}: {e}")

    return sorted(all_backups, key=lambda x: x[0], reverse=True)

def get_retention_plan(all_backups, num_latest, num_sundays):
    """Get folders to keep and delete"""
    if not all_backups:
        return []

    keep_dates = set()
    to_delete = []

    # Keep latest N backups
    for i, (date, folder) in enumerate(all_backups):
        if i < num_latest:
            keep_dates.add(date)
        elif date.weekday() == 6:  # Sunday
            keep_dates.add(date)
        else:
            to_delete.append((date, folder))

    # Ensure we have enough Sundays
    sundays_kept = sum(1 for date in keep_dates if date.weekday() == 6)
    if sundays_kept < num_sundays:
        # Move some Sunday deletions back to keep
        sunday_deletions = [(d, f) for d, f in to_delete if d.weekday() == 6]
        sunday_deletions.sort(key=lambda x: x[0], reverse=True)  # Newest first

        for i, (date, folder) in enumerate(sunday_deletions):
            if sundays_kept >= num_sundays:
                break
            keep_dates.add(date)
            to_delete.remove((date, folder))
            sundays_kept += 1

    return to_delete

def batch_delete_folders(s3_client, bucket_name, folders_to_delete):
    """Delete all objects in the specified folders"""
    total_objects = 0

    for _, folder in folders_to_delete:
        try:
            paginator = s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=bucket_name, Prefix=folder)

            objects_to_delete = []
            for page in pages:
                for obj in page.get('Contents', []):
                    objects_to_delete.append({'Key': obj['Key']})

                    # Delete in batches of 1000 (S3 limit)
                    if len(objects_to_delete) >= 1000:
                        # Uncomment to enable actual deletion:
                        # s3_client.delete_objects(Bucket=bucket_name, Delete={'Objects': objects_to_delete})
                        total_objects += len(objects_to_delete)
                        objects_to_delete = []

            # Delete remaining objects
            if objects_to_delete:
                # Uncomment to enable actual deletion:
                # s3_client.delete_objects(Bucket=bucket_name, Delete={'Objects': objects_to_delete})
                total_objects += len(objects_to_delete)

        except Exception as e:
            logger.error(f"Failed to delete {folder}: {e}")

    logger.info(f"Would delete {total_objects} objects")
    return total_objects

def process_prefix(prefix, bucket_name, num_latest, num_sundays):
    """Process a single prefix for parallel execution"""
    try:
        s3_client = get_s3_client()
        logger.info(f"Processing: {prefix}")

        # Get backup folders
        all_backups = get_backup_folders(s3_client, bucket_name, prefix, max_folders=100)
        if not all_backups:
            logger.info(f"No backups found for {prefix}")
            return {"prefix": prefix, "deleted": 0, "error": None}

        # Get folders to delete
        to_delete = get_retention_plan(all_backups, num_latest, num_sundays)
        if not to_delete:
            logger.info(f"No deletions needed for {prefix}")
            return {"prefix": prefix, "deleted": 0, "error": None}

        logger.info(f"Deleting {len(to_delete)} folders from {prefix}")

        # Delete all objects in the folders
        deleted_count = batch_delete_folders(s3_client, bucket_name, to_delete)

        return {"prefix": prefix, "deleted": deleted_count, "error": None}

    except Exception as e:
        logger.error(f"Error processing {prefix}: {e}")
        return {"prefix": prefix, "deleted": 0, "error": str(e)}

# -----------------------
# Main Airflow Callable
# -----------------------
# Main Function
def s3_retention_callable(**context):
    """Optimized S3 retention with parallel processing"""
    num_latest = context.get('num_latest', 6)
    num_sundays = context.get('num_sundays', 4)

    logger.info(f"Starting S3 retention: Keep {num_latest} latest + {num_sundays} Sundays")
    logger.info(f"Processing {len(PREFIXES)} prefixes in parallel")

    results = []
    with ThreadPoolExecutor(max_workers=min(len(PREFIXES), 5)) as executor:
        # Submit all prefix processing tasks
        future_to_prefix = {
            executor.submit(process_prefix, prefix, BUCKET, num_latest, num_sundays): prefix
            for prefix in PREFIXES
        }

        # Collect results
        for future in as_completed(future_to_prefix):
            prefix = future_to_prefix[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed {prefix}: {result['deleted']} objects")
            except Exception as e:
                logger.error(f"Failed {prefix}: {e}")
                results.append({"prefix": prefix, "deleted": 0, "error": str(e)})

    # Summary
    total_deleted = sum(r['deleted'] for r in results)
    failed_prefixes = [r['prefix'] for r in results if r['error']]

    logger.info(f"S3 retention completed: {total_deleted} total objects processed")
    if failed_prefixes:
        logger.warning(f"Failed prefixes: {failed_prefixes}")
    else:
        logger.info("All prefixes processed successfully")

# -----------------------
# DAG Definition
# -----------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="s3_retention",
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['s3', 'retention', 'utility'],
    description="Deletes old S3 backups based on a retention policy.",
) as dag:
    run_retention_script = PythonOperator(
        task_id="run_s3_retention_script",
        python_callable=s3_retention_callable,
        op_kwargs={
            'num_latest': 6,
            'num_sundays': 4,
        }
    )


