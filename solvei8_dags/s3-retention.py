from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from airflow.utils.dates import days_ago
from airflow.hooks.base import <PERSON>Hook
from datetime import datetime, timedelta, timezone
import logging
import re
import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON>Error, ClientError
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed
from collections import defaultdict

# -----------------------
# Configuration
# -----------------------
BUCKET = "solvei8-stateful-backups"
PREFIXES = [
    "mongo/prod/full_backups_mongo_plani8-4/",
    "mongo/prod/full_backups_prod_mongo-3/",
    "mongo/prod/full_backups_prod_mongo-4/",
    "mongo/stage/full_backups_mongo-3-3/",
    "mongo/stage/full_backups_mongo-4/",
]

logger = logging.getLogger(__name__)

# -----------------------
# S3 Client
# -----------------------
def get_s3_client(aws_conn_id='s3_creds'):
    """Get S3 client from Airflow connection"""
    connection = BaseHook.get_connection(aws_conn_id)
    extra_config = connection.extra_dejson if hasattr(connection, 'extra_dejson') else {}

    session_token = extra_config.get('aws_session_token')
    region_name = extra_config.get('region_name', 'ap-southeast-1')

    return boto3.client(
        's3',
        aws_access_key_id=connection.login,
        aws_secret_access_key=connection.password,
        aws_session_token=session_token,
        region_name=region_name
    )

# -----------------------
# Backup Folder Logic
# -----------------------
def get_backup_folders_optimized(s3_client, bucket_name, prefix, max_folders=50):
    """Get backup folders with early termination optimization"""
    all_backups = []
    pattern = re.compile(r'mongo-full-backup-(\d{8})T\d{6}/')
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/')

        for page in pages:
            for common_prefix in page.get('CommonPrefixes', []):
                folder = common_prefix['Prefix']
                match = pattern.search(folder)
                if match:
                    date_str = match.group(1)
                    try:
                        folder_date = datetime.strptime(date_str, "%Y%m%d").date()
                        all_backups.append((folder_date, folder))

                        # Early termination: stop if we have enough folders
                        if len(all_backups) >= max_folders:
                            logger.info(f"Early termination: Found {max_folders} folders for {prefix}")
                            break
                    except ValueError:
                        logger.warning(f"Could not parse date from folder name: {folder}")

            # Break outer loop if we hit the limit
            if len(all_backups) >= max_folders:
                break

    except (BotoCoreError, ClientError) as e:
        logger.error(f"Error listing objects for prefix {prefix}: {e}")

    # Sort by date (newest first) for better early termination effectiveness
    return sorted(all_backups, key=lambda x: x[0], reverse=True)

# -----------------------
# Retention Policy Logic
# -----------------------
def get_keep_dates_optimized(all_backups, num_latest, num_sundays):
    """Optimized retention logic with early termination"""
    if not all_backups:
        return set(), []

    # all_backups is already sorted (newest first) from get_backup_folders_optimized
    keep_dates = set()
    to_delete = []

    # Get latest N backups
    latest_count = 0
    for date, folder in all_backups:
        if latest_count < num_latest:
            keep_dates.add(date)
            latest_count += 1
        else:
            # Check if it's a Sunday we want to keep
            if date.weekday() == 6:  # Sunday
                keep_dates.add(date)
            else:
                to_delete.append((date, folder))

    # Get additional Sundays if needed
    sundays_found = sum(1 for date in keep_dates if date.weekday() == 6)
    if sundays_found < num_sundays:
        current_date = datetime.now(timezone.utc).date()
        while sundays_found < num_sundays and current_date >= min(date for date, _ in all_backups):
            if current_date.weekday() == 6 and current_date not in keep_dates:
                # Find this Sunday in our backups and move from to_delete to keep
                for i, (date, folder) in enumerate(to_delete):
                    if date == current_date:
                        keep_dates.add(date)
                        to_delete.pop(i)
                        sundays_found += 1
                        break
            current_date -= timedelta(days=1)

    return keep_dates, to_delete

def batch_delete_objects(s3_client, bucket_name, folders_to_delete, batch_size=1000):
    """Batch delete objects across multiple folders"""
    all_objects = []

    # Collect all objects from all folders
    for _, folder in folders_to_delete:
        try:
            paginator = s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=bucket_name, Prefix=folder)

            for page in pages:
                for obj in page.get('Contents', []):
                    all_objects.append({'Key': obj['Key']})

                    # Process in batches to avoid memory issues
                    if len(all_objects) >= batch_size:
                        _delete_batch(s3_client, bucket_name, all_objects)
                        all_objects = []

        except Exception as e:
            logger.error(f"Failed to list objects in {folder}: {e}")

    # Delete remaining objects
    if all_objects:
        _delete_batch(s3_client, bucket_name, all_objects)

def _delete_batch(s3_client, bucket_name, objects):
    """Delete a batch of objects"""
    try:
        if objects:
            # Uncomment the next line to enable actual deletion
            # s3_client.delete_objects(Bucket=bucket_name, Delete={'Objects': objects})
            logger.info(f"Would delete {len(objects)} objects")
    except Exception as e:
        logger.error(f"Failed to delete batch of {len(objects)} objects: {e}")

def process_single_prefix(prefix, bucket_name, num_latest, num_sundays):
    """Process a single prefix - designed for parallel execution"""
    try:
        s3_client = get_s3_client()  # Each thread gets its own client
        logger.info(f"Processing: {prefix}")

        # Get backups with early termination
        all_backups = get_backup_folders_optimized(s3_client, bucket_name, prefix, max_folders=100)
        if not all_backups:
            logger.info(f"No backups found for {prefix}")
            return {"prefix": prefix, "deleted": 0, "error": None}

        # Get retention decisions
        keep_dates, to_delete = get_keep_dates_optimized(all_backups, num_latest, num_sundays)

        if not to_delete:
            logger.info(f"No deletions needed for {prefix}")
            return {"prefix": prefix, "deleted": 0, "error": None}

        logger.info(f"Deleting {len(to_delete)} folders from {prefix}")

        # Batch delete all objects
        batch_delete_objects(s3_client, bucket_name, to_delete)

        return {"prefix": prefix, "deleted": len(to_delete), "error": None}

    except Exception as e:
        logger.error(f"Error processing prefix {prefix}: {e}")
        return {"prefix": prefix, "deleted": 0, "error": str(e)}

# -----------------------
# Main Airflow Callable
# -----------------------
def s3_retention_callable(**context):
    """Optimized S3 retention with parallel processing"""
    num_latest = context.get('num_latest', 6)
    num_sundays = context.get('num_sundays', 4)

    logger.info(f"Starting optimized S3 retention: Keep {num_latest} latest + {num_sundays} Sundays")
    logger.info(f"Processing {len(PREFIXES)} prefixes in parallel")

    # Process all prefixes in parallel
    results = []
    with ThreadPoolExecutor(max_workers=min(len(PREFIXES), 5)) as executor:
        # Submit all tasks
        future_to_prefix = {
            executor.submit(process_single_prefix, prefix, BUCKET, num_latest, num_sundays): prefix
            for prefix in PREFIXES
        }

        # Collect results as they complete
        for future in as_completed(future_to_prefix):
            prefix = future_to_prefix[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed {prefix}: {result['deleted']} folders deleted")
            except Exception as e:
                logger.error(f"Failed to process {prefix}: {e}")
                results.append({"prefix": prefix, "deleted": 0, "error": str(e)})

    # Summary
    total_deleted = sum(r['deleted'] for r in results)
    failed_prefixes = [r['prefix'] for r in results if r['error']]

    logger.info(f"S3 retention completed: {total_deleted} total folders deleted")
    if failed_prefixes:
        logger.warning(f"Failed prefixes: {failed_prefixes}")
    else:
        logger.info("All prefixes processed successfully")

# -----------------------
# DAG Definition
# -----------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="s3_retention",
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['s3', 'retention', 'utility'],
    description="Deletes old S3 backups based on a retention policy.",
) as dag:
    run_retention_script = PythonOperator(
        task_id="run_s3_retention_script",
        python_callable=s3_retention_callable,
        op_kwargs={
            'num_latest': 6,
            'num_sundays': 4,
        }
    )


