from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from datetime import datetime, timedelta, timezone
import logging
import re
from botocore.exceptions import BotoCoreError, ClientError

# -----------------------
# Configuration
# -----------------------
BUCKET = "solvei8-stateful-backups"
PREFIXES = [
    "mongo/prod/full_backups_mongo_plani8-4/",
    "mongo/prod/full_backups_prod_mongo-3/",
    "mongo/prod/full_backups_prod_mongo-4/",
    "mongo/stage/full_backups_mongo-3-3/",
    "mongo/stage/full_backups_mongo-4/",
]

# -----------------------
# Logging Setup
# -----------------------
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

# -----------------------
# S3 Client
# -----------------------
def get_s3_client(aws_conn_id='s3_creds'):
    """
    Initializes and returns a boto3 S3 client using Airflow connections.

    Args:
        aws_conn_id (str): Airflow connection ID for AWS credentials

    Returns:
        boto3.client: Configured S3 client
    """
    try:
        

        # Use Airflow's S3Hook to get credentials from connection
        s3_hook = S3Hook(aws_conn_id=aws_conn_id)

        # Get the underlying boto3 client with proper credentials
        return s3_hook.get_conn()

    except Exception as e:
        logger.error(f"Failed to initialize S3 client using connection '{aws_conn_id}': {e}")
        logger.info("Make sure you have configured the AWS connection in Airflow Admin -> Connections")
        raise

# -----------------------
# Backup Folder Logic
# -----------------------
def get_backup_folders(s3_client, bucket_name, prefix):
    all_backups = []
    pattern = re.compile(r'mongo-full-backup-(\d{8})T\d{6}/')
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/')
        for page in pages:
            for common_prefix in page.get('CommonPrefixes', []):
                folder = common_prefix['Prefix']
                match = pattern.search(folder)
                if match:
                    date_str = match.group(1)
                    try:
                        folder_date = datetime.strptime(date_str, "%Y%m%d").date()
                        all_backups.append((folder_date, folder))
                    except ValueError:
                        logger.warning(f"Could not parse date from folder name: {folder}")
    except (BotoCoreError, ClientError) as e:
        logger.error(f"Error listing objects for prefix {prefix}: {e}")
    return all_backups

# -----------------------
# Retention Policy Logic
# -----------------------
def get_keep_dates(all_backups, num_latest, num_sundays):
    if not all_backups:
        return set()
    sorted_backups = sorted(all_backups, key=lambda x: x[0], reverse=True)
    latest_dates = set()
    for date, _ in sorted_backups:
        if len(latest_dates) < num_latest:
            latest_dates.add(date)
        else:
            break
    sundays_to_keep = set()
    current_date = datetime.now(timezone.utc).date()
    while len(sundays_to_keep) < num_sundays:
        if current_date.weekday() == 6:  # Sunday is 6
            sundays_to_keep.add(current_date)
        current_date -= timedelta(days=1)
    return latest_dates.union(sundays_to_keep)

def find_deletions(all_backups, keep_dates):
    to_delete = []
    for date, folder in all_backups:
        if date not in keep_dates:
            to_delete.append((date, folder))
    return to_delete

# -----------------------
# Main Airflow Callable
# -----------------------
def s3_retention_callable(**context):
    """
    This function is called by the Airflow PythonOperator.
    It receives parameters from op_kwargs via context.
    """
    # Extract parameters from context
    num_latest = context.get('num_latest', 6)
    num_sundays = context.get('num_sundays', 4)
    s3_client = get_s3_client()
    
    logger.info(f"Starting S3 backup retention for bucket: {BUCKET}")
    logger.info(f"Retention Policy: Keep latest {num_latest} and last {num_sundays} Sundays.")

    for prefix in PREFIXES:
        logger.info(f"\n--- Processing prefix: {prefix} ---")
        all_backups_for_prefix = get_backup_folders(s3_client, BUCKET, prefix)
        if not all_backups_for_prefix:
            logger.info(f"No backup folders found. Skipping.")
            continue

        keep_dates_for_prefix = get_keep_dates(all_backups_for_prefix, num_latest, num_sundays)
        to_delete_for_prefix = find_deletions(all_backups_for_prefix, keep_dates_for_prefix)

        logger.info(f"--- Deleting {len(to_delete_for_prefix)} folders in {prefix} ---")
        for date, folder in to_delete_for_prefix:
            logger.info(f"DELETING: s3://{BUCKET}/{folder} (Date: {date})")
            # To delete a "folder", you must delete all objects within it.
            try:
                paginator = s3_client.get_paginator('list_objects_v2')
                pages = paginator.paginate(Bucket=BUCKET, Prefix=folder)
                objects_to_delete = {'Objects': [{'Key': obj['Key']} for page in pages for obj in page.get('Contents', [])]}
                
                if objects_to_delete['Objects']:
                    s3_client.delete_objects(Bucket=BUCKET, Delete=objects_to_delete)
                    logger.info(f"Successfully deleted objects in s3://{BUCKET}/{folder}")
            except (BotoCoreError, ClientError) as e:
                logger.error(f"Failed to delete objects in s3://{BUCKET}/{folder}: {e}")

    logger.info("S3 backup retention script finished.")

# -----------------------
# DAG Definition
# -----------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="s3_retention",
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['s3', 'retention', 'utility'],
    description="Deletes old S3 backups based on a retention policy.",
) as dag:
    run_retention_script = PythonOperator(
        task_id="run_s3_retention_script",
        python_callable=s3_retention_callable,
        op_kwargs={
            'num_latest': 6,
            'num_sundays': 4,
        }
    )


